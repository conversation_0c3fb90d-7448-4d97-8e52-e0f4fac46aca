import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Position } from '../types';

interface UIState {
  // Selection state
  selectedSlideId: string | null;
  selectedElementIds: string[];
  selectedLayerId: string | null;
  
  // Panel state
  activePanel: 'properties' | 'assets' | 'timeline' | null;
  sidebarCollapsed: {
    left: boolean;
    right: boolean;
  };
  timelineCollapsed: boolean;
  
  // Canvas state
  canvasZoom: number;
  canvasPosition: Position;
  canvasFitMode: 'fit' | 'fill' | 'actual';
  
  // Application state
  previewMode: boolean;
  isPlaying: boolean;
  currentTime: number; // for timeline playback
  
  // Tool state
  activeTool: 'select' | 'text' | 'shape' | 'image' | 'video' | 'audio' | 'button' | 'hotspot' | 'quiz';
  
  // Modal/Dialog state
  showNewProjectDialog: boolean;
  showExportDialog: boolean;
  showSettingsDialog: boolean;
  
  // Clipboard
  clipboard: any[];
  
  // History
  canUndo: boolean;
  canRedo: boolean;
}

interface UIActions {
  // Selection actions
  setSelectedSlide: (slideId: string | null) => void;
  setSelectedElements: (elementIds: string[]) => void;
  addSelectedElement: (elementId: string) => void;
  removeSelectedElement: (elementId: string) => void;
  clearSelection: () => void;
  setSelectedLayer: (layerId: string | null) => void;
  
  // Panel actions
  setActivePanel: (panel: UIState['activePanel']) => void;
  toggleSidebar: (side: 'left' | 'right') => void;
  toggleTimeline: () => void;
  
  // Canvas actions
  setCanvasZoom: (zoom: number) => void;
  setCanvasPosition: (position: Position) => void;
  setCanvasFitMode: (mode: UIState['canvasFitMode']) => void;
  zoomIn: () => void;
  zoomOut: () => void;
  zoomToFit: () => void;
  zoomToActual: () => void;
  
  // Application actions
  setPreviewMode: (preview: boolean) => void;
  setIsPlaying: (playing: boolean) => void;
  setCurrentTime: (time: number) => void;
  
  // Tool actions
  setActiveTool: (tool: UIState['activeTool']) => void;
  
  // Modal/Dialog actions
  setShowNewProjectDialog: (show: boolean) => void;
  setShowExportDialog: (show: boolean) => void;
  setShowSettingsDialog: (show: boolean) => void;
  
  // Clipboard actions
  copyToClipboard: (items: any[]) => void;
  clearClipboard: () => void;
  
  // History actions
  setCanUndo: (canUndo: boolean) => void;
  setCanRedo: (canRedo: boolean) => void;
  
  // Utility actions
  resetUI: () => void;
}

type UIStore = UIState & UIActions;

const initialState: UIState = {
  // Selection state
  selectedSlideId: null,
  selectedElementIds: [],
  selectedLayerId: null,
  
  // Panel state
  activePanel: null,
  sidebarCollapsed: {
    left: false,
    right: false,
  },
  timelineCollapsed: true,
  
  // Canvas state
  canvasZoom: 1,
  canvasPosition: { x: 0, y: 0 },
  canvasFitMode: 'fit',
  
  // Application state
  previewMode: false,
  isPlaying: false,
  currentTime: 0,
  
  // Tool state
  activeTool: 'select',
  
  // Modal/Dialog state
  showNewProjectDialog: false,
  showExportDialog: false,
  showSettingsDialog: false,
  
  // Clipboard
  clipboard: [],
  
  // History
  canUndo: false,
  canRedo: false,
};

export const useUIStore = create<UIStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Selection actions
      setSelectedSlide: (slideId) => {
        set({ selectedSlideId: slideId });
      },

      setSelectedElements: (elementIds) => {
        set({ selectedElementIds: elementIds });
      },

      addSelectedElement: (elementId) => {
        const { selectedElementIds } = get();
        if (!selectedElementIds.includes(elementId)) {
          set({ selectedElementIds: [...selectedElementIds, elementId] });
        }
      },

      removeSelectedElement: (elementId) => {
        const { selectedElementIds } = get();
        set({ selectedElementIds: selectedElementIds.filter(id => id !== elementId) });
      },

      clearSelection: () => {
        set({ selectedElementIds: [], selectedLayerId: null });
      },

      setSelectedLayer: (layerId) => {
        set({ selectedLayerId: layerId });
      },

      // Panel actions
      setActivePanel: (panel) => {
        set({ activePanel: panel });
      },

      toggleSidebar: (side) => {
        const { sidebarCollapsed } = get();
        set({
          sidebarCollapsed: {
            ...sidebarCollapsed,
            [side]: !sidebarCollapsed[side],
          },
        });
      },

      toggleTimeline: () => {
        const { timelineCollapsed } = get();
        set({ timelineCollapsed: !timelineCollapsed });
      },

      // Canvas actions
      setCanvasZoom: (zoom) => {
        set({ canvasZoom: Math.max(0.1, Math.min(5, zoom)) });
      },

      setCanvasPosition: (position) => {
        set({ canvasPosition: position });
      },

      setCanvasFitMode: (mode) => {
        set({ canvasFitMode: mode });
      },

      zoomIn: () => {
        const { canvasZoom } = get();
        const newZoom = Math.min(5, canvasZoom * 1.2);
        set({ canvasZoom: newZoom });
      },

      zoomOut: () => {
        const { canvasZoom } = get();
        const newZoom = Math.max(0.1, canvasZoom / 1.2);
        set({ canvasZoom: newZoom });
      },

      zoomToFit: () => {
        set({ canvasFitMode: 'fit', canvasZoom: 1 });
      },

      zoomToActual: () => {
        set({ canvasFitMode: 'actual', canvasZoom: 1 });
      },

      // Application actions
      setPreviewMode: (preview) => {
        set({ previewMode: preview });
        if (preview) {
          set({ selectedElementIds: [], activeTool: 'select' });
        }
      },

      setIsPlaying: (playing) => {
        set({ isPlaying: playing });
      },

      setCurrentTime: (time) => {
        set({ currentTime: time });
      },

      // Tool actions
      setActiveTool: (tool) => {
        set({ activeTool: tool });
        if (tool !== 'select') {
          set({ selectedElementIds: [] });
        }
      },

      // Modal/Dialog actions
      setShowNewProjectDialog: (show) => {
        set({ showNewProjectDialog: show });
      },

      setShowExportDialog: (show) => {
        set({ showExportDialog: show });
      },

      setShowSettingsDialog: (show) => {
        set({ showSettingsDialog: show });
      },

      // Clipboard actions
      copyToClipboard: (items) => {
        set({ clipboard: items });
      },

      clearClipboard: () => {
        set({ clipboard: [] });
      },

      // History actions
      setCanUndo: (canUndo) => {
        set({ canUndo });
      },

      setCanRedo: (canRedo) => {
        set({ canRedo });
      },

      // Utility actions
      resetUI: () => {
        set(initialState);
      },
    }),
    {
      name: 'ui-store',
    }
  )
);

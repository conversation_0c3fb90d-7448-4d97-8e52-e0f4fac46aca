import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Project, Slide, Asset, ProjectSettings, Size } from '../types';

interface ProjectState {
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
}

interface ProjectActions {
  // Project management
  createNewProject: (settings: Partial<ProjectSettings>) => void;
  loadProject: (project: Project) => void;
  updateProject: (updates: Partial<Project>) => void;
  clearProject: () => void;
  
  // Slide management
  addSlide: (slide?: Partial<Slide>) => void;
  updateSlide: (slideId: string, updates: Partial<Slide>) => void;
  deleteSlide: (slideId: string) => void;
  duplicateSlide: (slideId: string) => void;
  reorderSlides: (fromIndex: number, toIndex: number) => void;
  
  // Asset management
  addAsset: (asset: Asset) => void;
  updateAsset: (assetId: string, updates: Partial<Asset>) => void;
  deleteAsset: (assetId: string) => void;
  
  // Utility
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type ProjectStore = ProjectState & ProjectActions;

const defaultProjectSettings: ProjectSettings = {
  title: 'New Project',
  description: '',
  author: 'Author',
  version: '1.0.0',
  defaultSlideSize: { width: 1920, height: 1080 },
  backgroundColor: '#ffffff',
  language: 'en',
  publishSettings: {
    format: 'html5',
    responsive: true,
    showNavigation: true,
    showProgress: true,
  },
};

const createDefaultSlide = (index: number = 0, size: Size = { width: 1920, height: 1080 }): Slide => ({
  id: `slide-${Date.now()}-${index}`,
  name: `Slide ${index + 1}`,
  layers: [
    {
      id: `layer-${Date.now()}-base`,
      name: 'Base Layer',
      elements: [],
      visible: true,
      locked: false,
      opacity: 1,
    },
  ],
  background: {
    type: 'color',
    color: '#ffffff',
  },
  dimensions: size,
  notes: '',
});

export const useProjectStore = create<ProjectStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentProject: null,
      isLoading: false,
      error: null,

      // Project management
      createNewProject: (settings) => {
        const newProject: Project = {
          id: `project-${Date.now()}`,
          settings: { ...defaultProjectSettings, ...settings },
          slides: [createDefaultSlide(0, settings.defaultSlideSize || defaultProjectSettings.defaultSlideSize)],
          assets: [],
          variables: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        set({ currentProject: newProject, error: null });
      },

      loadProject: (project) => {
        set({ currentProject: project, error: null });
      },

      updateProject: (updates) => {
        const { currentProject } = get();
        if (!currentProject) return;

        set({
          currentProject: {
            ...currentProject,
            ...updates,
            updatedAt: new Date(),
          },
        });
      },

      clearProject: () => {
        set({ currentProject: null, error: null });
      },

      // Slide management
      addSlide: (slideData) => {
        const { currentProject } = get();
        if (!currentProject) return;

        const newSlide = createDefaultSlide(
          currentProject.slides.length,
          currentProject.settings.defaultSlideSize
        );

        if (slideData) {
          Object.assign(newSlide, slideData);
        }

        set({
          currentProject: {
            ...currentProject,
            slides: [...currentProject.slides, newSlide],
            updatedAt: new Date(),
          },
        });
      },

      updateSlide: (slideId, updates) => {
        const { currentProject } = get();
        if (!currentProject) return;

        const slideIndex = currentProject.slides.findIndex(slide => slide.id === slideId);
        if (slideIndex === -1) return;

        const updatedSlides = [...currentProject.slides];
        updatedSlides[slideIndex] = { ...updatedSlides[slideIndex], ...updates };

        set({
          currentProject: {
            ...currentProject,
            slides: updatedSlides,
            updatedAt: new Date(),
          },
        });
      },

      deleteSlide: (slideId) => {
        const { currentProject } = get();
        if (!currentProject || currentProject.slides.length <= 1) return;

        set({
          currentProject: {
            ...currentProject,
            slides: currentProject.slides.filter(slide => slide.id !== slideId),
            updatedAt: new Date(),
          },
        });
      },

      duplicateSlide: (slideId) => {
        const { currentProject } = get();
        if (!currentProject) return;

        const slideIndex = currentProject.slides.findIndex(slide => slide.id === slideId);
        if (slideIndex === -1) return;

        const originalSlide = currentProject.slides[slideIndex];
        const duplicatedSlide: Slide = {
          ...originalSlide,
          id: `slide-${Date.now()}`,
          name: `${originalSlide.name} (Copy)`,
          layers: originalSlide.layers.map(layer => ({
            ...layer,
            id: `layer-${Date.now()}-${Math.random()}`,
            elements: layer.elements.map(element => ({
              ...element,
              id: `element-${Date.now()}-${Math.random()}`,
            })),
          })),
        };

        const updatedSlides = [...currentProject.slides];
        updatedSlides.splice(slideIndex + 1, 0, duplicatedSlide);

        set({
          currentProject: {
            ...currentProject,
            slides: updatedSlides,
            updatedAt: new Date(),
          },
        });
      },

      reorderSlides: (fromIndex, toIndex) => {
        const { currentProject } = get();
        if (!currentProject) return;

        const slides = [...currentProject.slides];
        const [movedSlide] = slides.splice(fromIndex, 1);
        slides.splice(toIndex, 0, movedSlide);

        set({
          currentProject: {
            ...currentProject,
            slides,
            updatedAt: new Date(),
          },
        });
      },

      // Asset management
      addAsset: (asset) => {
        const { currentProject } = get();
        if (!currentProject) return;

        set({
          currentProject: {
            ...currentProject,
            assets: [...currentProject.assets, asset],
            updatedAt: new Date(),
          },
        });
      },

      updateAsset: (assetId, updates) => {
        const { currentProject } = get();
        if (!currentProject) return;

        const assetIndex = currentProject.assets.findIndex(asset => asset.id === assetId);
        if (assetIndex === -1) return;

        const updatedAssets = [...currentProject.assets];
        updatedAssets[assetIndex] = { ...updatedAssets[assetIndex], ...updates };

        set({
          currentProject: {
            ...currentProject,
            assets: updatedAssets,
            updatedAt: new Date(),
          },
        });
      },

      deleteAsset: (assetId) => {
        const { currentProject } = get();
        if (!currentProject) return;

        set({
          currentProject: {
            ...currentProject,
            assets: currentProject.assets.filter(asset => asset.id !== assetId),
            updatedAt: new Date(),
          },
        });
      },

      // Utility
      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      setError: (error) => {
        set({ error });
      },
    }),
    {
      name: 'project-store',
    }
  )
);

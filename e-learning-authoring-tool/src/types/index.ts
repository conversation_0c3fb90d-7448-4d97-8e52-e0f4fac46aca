// Core data models for the e-learning authoring tool

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Transform {
  position: Position;
  size: Size;
  rotation: number;
  scale: number;
}

// Animation types
export type EasingType = 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bounce' | 'elastic';
export type AnimationType = 'fade' | 'slide' | 'scale' | 'rotate' | 'bounce' | 'flip';

export interface Keyframe {
  id: string;
  time: number; // in milliseconds
  properties: Record<string, any>;
  easing: EasingType;
}

export interface Animation {
  id: string;
  name: string;
  type: AnimationType;
  duration: number; // in milliseconds
  delay: number;
  keyframes: Keyframe[];
  loop: boolean;
  autoplay: boolean;
}

// Element types
export type ElementType = 'text' | 'image' | 'video' | 'audio' | 'button' | 'hotspot' | 'quiz' | 'shape';

export interface BaseElement {
  id: string;
  type: ElementType;
  name: string;
  transform: Transform;
  visible: boolean;
  locked: boolean;
  opacity: number;
  zIndex: number;
  animations: Animation[];
  interactions: Interaction[];
}

export interface TextElement extends BaseElement {
  type: 'text';
  content: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
  fontStyle: 'normal' | 'italic';
  textAlign: 'left' | 'center' | 'right' | 'justify';
  color: string;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface ImageElement extends BaseElement {
  type: 'image';
  src: string;
  alt: string;
  fit: 'contain' | 'cover' | 'fill' | 'scale-down';
  borderRadius?: number;
  shadow?: {
    offsetX: number;
    offsetY: number;
    blur: number;
    color: string;
  };
}

export interface VideoElement extends BaseElement {
  type: 'video';
  src: string;
  poster?: string;
  autoplay: boolean;
  loop: boolean;
  muted: boolean;
  controls: boolean;
}

export interface AudioElement extends BaseElement {
  type: 'audio';
  src: string;
  autoplay: boolean;
  loop: boolean;
  controls: boolean;
  volume: number;
}

export interface ButtonElement extends BaseElement {
  type: 'button';
  text: string;
  backgroundColor: string;
  textColor: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius: number;
  fontSize: number;
  fontFamily: string;
  fontWeight: string;
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  hoverStyle?: {
    backgroundColor?: string;
    textColor?: string;
    borderColor?: string;
  };
}

export interface HotspotElement extends BaseElement {
  type: 'hotspot';
  shape: 'rectangle' | 'circle' | 'polygon';
  fillColor: string;
  strokeColor: string;
  strokeWidth: number;
  tooltip?: string;
}

export interface QuizElement extends BaseElement {
  type: 'quiz';
  question: string;
  questionType: 'multiple-choice' | 'true-false' | 'fill-blank' | 'drag-drop';
  options: QuizOption[];
  correctAnswers: string[];
  feedback: {
    correct: string;
    incorrect: string;
  };
  attempts: number;
  showFeedback: boolean;
}

export interface QuizOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface ShapeElement extends BaseElement {
  type: 'shape';
  shapeType: 'rectangle' | 'circle' | 'triangle' | 'line' | 'arrow';
  fillColor: string;
  strokeColor: string;
  strokeWidth: number;
  borderRadius?: number;
}

export type Element = TextElement | ImageElement | VideoElement | AudioElement | ButtonElement | HotspotElement | QuizElement | ShapeElement;

// Interaction types
export type TriggerType = 'click' | 'hover' | 'load' | 'timer' | 'key-press';
export type ActionType = 'navigate' | 'show' | 'hide' | 'animate' | 'play-audio' | 'pause-audio' | 'show-layer' | 'hide-layer' | 'toggle-variable';

export interface Interaction {
  id: string;
  trigger: TriggerType;
  action: ActionType;
  target?: string; // element ID or slide ID
  parameters?: Record<string, any>;
  conditions?: Condition[];
}

export interface Condition {
  variable: string;
  operator: '==' | '!=' | '>' | '<' | '>=' | '<=';
  value: any;
}

// Layer and Slide types
export interface Layer {
  id: string;
  name: string;
  elements: Element[];
  visible: boolean;
  locked: boolean;
  opacity: number;
}

export interface SlideBackground {
  type: 'color' | 'image' | 'gradient';
  color?: string;
  image?: string;
  gradient?: {
    type: 'linear' | 'radial';
    colors: string[];
    direction?: number; // degrees for linear
  };
}

export interface Slide {
  id: string;
  name: string;
  layers: Layer[];
  background: SlideBackground;
  dimensions: Size;
  duration?: number; // for auto-advance
  notes?: string;
  thumbnail?: string;
}

// Project and Asset types
export interface Asset {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  size: number;
  dimensions?: Size;
  duration?: number; // for video/audio
  uploadedAt: Date;
}

export interface ProjectSettings {
  title: string;
  description?: string;
  author: string;
  version: string;
  defaultSlideSize: Size;
  backgroundColor: string;
  language: string;
  publishSettings: {
    format: 'html5' | 'scorm' | 'xapi';
    responsive: boolean;
    showNavigation: boolean;
    showProgress: boolean;
  };
}

export interface Project {
  id: string;
  settings: ProjectSettings;
  slides: Slide[];
  assets: Asset[];
  variables: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// UI State types
export interface UIState {
  selectedSlideId: string | null;
  selectedElementIds: string[];
  selectedLayerId: string | null;
  activePanel: 'properties' | 'assets' | 'timeline' | null;
  canvasZoom: number;
  canvasPosition: Position;
  sidebarCollapsed: {
    left: boolean;
    right: boolean;
  };
  timelineCollapsed: boolean;
  previewMode: boolean;
}

// Store types
export interface ProjectStore {
  currentProject: Project | null;
  setProject: (project: Project) => void;
  updateProject: (updates: Partial<Project>) => void;
  addSlide: (slide: Slide) => void;
  updateSlide: (slideId: string, updates: Partial<Slide>) => void;
  deleteSlide: (slideId: string) => void;
  reorderSlides: (fromIndex: number, toIndex: number) => void;
  addAsset: (asset: Asset) => void;
  deleteAsset: (assetId: string) => void;
}

export interface UIStore {
  ui: UIState;
  setSelectedSlide: (slideId: string | null) => void;
  setSelectedElements: (elementIds: string[]) => void;
  setSelectedLayer: (layerId: string | null) => void;
  setActivePanel: (panel: UIState['activePanel']) => void;
  setCanvasZoom: (zoom: number) => void;
  setCanvasPosition: (position: Position) => void;
  toggleSidebar: (side: 'left' | 'right') => void;
  toggleTimeline: () => void;
  setPreviewMode: (preview: boolean) => void;
}

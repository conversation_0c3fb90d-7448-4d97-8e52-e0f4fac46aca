import React, { useRef } from 'react';
import { Box, Typography } from '@mui/material';
import { useDrag } from 'react-dnd';
import { Element, TextElement, ImageElement, ButtonElement, ShapeElement } from '../../types';
import { useUIStore } from '../../store';

interface DraggableElementProps {
  element: Element;
  onUpdate: (elementId: string, updates: Partial<Element>) => void;
}

const DraggableElement: React.FC<DraggableElementProps> = ({ element, onUpdate }) => {
  const { selectedElementIds, setSelectedElements } = useUIStore();
  const elementRef = useRef<HTMLDivElement>(null);
  
  const isSelected = selectedElementIds.includes(element.id);

  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'CANVAS_ELEMENT',
    item: { id: element.id, type: element.type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (e.ctrlKey || e.metaKey) {
      // Multi-select
      if (isSelected) {
        setSelectedElements(selectedElementIds.filter(id => id !== element.id));
      } else {
        setSelectedElements([...selectedElementIds, element.id]);
      }
    } else {
      // Single select
      setSelectedElements([element.id]);
    }
  };

  const renderElementContent = () => {
    switch (element.type) {
      case 'text':
        const textElement = element as TextElement;
        return (
          <Typography
            sx={{
              fontSize: `${textElement.fontSize}px`,
              fontFamily: textElement.fontFamily,
              fontWeight: textElement.fontWeight,
              fontStyle: textElement.fontStyle,
              textAlign: textElement.textAlign,
              color: textElement.color,
              backgroundColor: textElement.backgroundColor,
              padding: `${textElement.padding.top}px ${textElement.padding.right}px ${textElement.padding.bottom}px ${textElement.padding.left}px`,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: textElement.textAlign === 'center' ? 'center' : textElement.textAlign === 'right' ? 'flex-end' : 'flex-start',
              overflow: 'hidden',
              wordBreak: 'break-word',
            }}
          >
            {textElement.content || 'Text'}
          </Typography>
        );

      case 'image':
        const imageElement = element as ImageElement;
        return (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundColor: '#f0f0f0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px dashed #ccc',
            }}
          >
            {imageElement.src ? (
              <img
                src={imageElement.src}
                alt={imageElement.alt}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: imageElement.fit,
                }}
              />
            ) : (
              <Typography variant="caption" color="text.secondary">
                Image
              </Typography>
            )}
          </Box>
        );

      case 'button':
        const buttonElement = element as ButtonElement;
        return (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundColor: buttonElement.backgroundColor,
              color: buttonElement.textColor,
              border: buttonElement.borderColor ? `${buttonElement.borderWidth || 1}px solid ${buttonElement.borderColor}` : 'none',
              borderRadius: `${buttonElement.borderRadius}px`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              fontSize: `${buttonElement.fontSize}px`,
              fontFamily: buttonElement.fontFamily,
              fontWeight: buttonElement.fontWeight,
              padding: `${buttonElement.padding.top}px ${buttonElement.padding.right}px ${buttonElement.padding.bottom}px ${buttonElement.padding.left}px`,
              '&:hover': {
                backgroundColor: buttonElement.hoverStyle?.backgroundColor || buttonElement.backgroundColor,
                color: buttonElement.hoverStyle?.textColor || buttonElement.textColor,
              },
            }}
          >
            {buttonElement.text}
          </Box>
        );

      case 'shape':
        const shapeElement = element as ShapeElement;
        return (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundColor: shapeElement.fillColor,
              border: `${shapeElement.strokeWidth}px solid ${shapeElement.strokeColor}`,
              borderRadius: shapeElement.shapeType === 'circle' ? '50%' : `${shapeElement.borderRadius || 0}px`,
            }}
          />
        );

      default:
        return (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(200, 200, 200, 0.3)',
              border: '1px dashed #ccc',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography variant="caption" color="text.secondary">
              {element.type}
            </Typography>
          </Box>
        );
    }
  };

  return (
    <Box
      ref={(node) => {
        elementRef.current = node;
        drag(node);
      }}
      onClick={handleClick}
      sx={{
        position: 'absolute',
        left: element.transform.position.x,
        top: element.transform.position.y,
        width: element.transform.size.width,
        height: element.transform.size.height,
        transform: `rotate(${element.transform.rotation}deg) scale(${element.transform.scale})`,
        opacity: isDragging ? 0.5 : element.opacity,
        zIndex: element.zIndex,
        cursor: isDragging ? 'grabbing' : 'grab',
        border: isSelected ? '2px solid #1976d2' : '1px solid transparent',
        '&:hover': {
          border: isSelected ? '2px solid #1976d2' : '1px solid #1976d2',
        },
      }}
    >
      {renderElementContent()}
      
      {/* Selection handles */}
      {isSelected && (
        <>
          {/* Corner handles for resizing */}
          <Box
            sx={{
              position: 'absolute',
              top: -4,
              left: -4,
              width: 8,
              height: 8,
              backgroundColor: '#1976d2',
              cursor: 'nw-resize',
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              top: -4,
              right: -4,
              width: 8,
              height: 8,
              backgroundColor: '#1976d2',
              cursor: 'ne-resize',
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: -4,
              left: -4,
              width: 8,
              height: 8,
              backgroundColor: '#1976d2',
              cursor: 'sw-resize',
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: -4,
              right: -4,
              width: 8,
              height: 8,
              backgroundColor: '#1976d2',
              cursor: 'se-resize',
            }}
          />
        </>
      )}
    </Box>
  );
};

export default DraggableElement;

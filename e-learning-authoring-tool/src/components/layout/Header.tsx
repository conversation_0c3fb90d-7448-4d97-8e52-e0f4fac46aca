import React from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  Tool<PERSON>,
  Typography,
  Button,
  IconButton,
  Box,
  Divider,
  Tooltip,
} from '@mui/material';
import {
  Save as SaveIcon,
  FolderOpen as OpenIcon,
  PlayArrow as PreviewIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  Settings as SettingsIcon,
  FileDownload as ExportIcon,
} from '@mui/icons-material';
import { useProjectStore, useUIStore } from '../../store';

const Header: React.FC = () => {
  const { currentProject } = useProjectStore();
  const { 
    previewMode, 
    canUndo, 
    canRedo, 
    setPreviewMode, 
    setShowNewProjectDialog,
    setShowExportDialog,
    setShowSettingsDialog 
  } = useUIStore();

  const handleNewProject = () => {
    setShowNewProjectDialog(true);
  };

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Save project');
  };

  const handleOpen = () => {
    // TODO: Implement open functionality
    console.log('Open project');
  };

  const handlePreview = () => {
    setPreviewMode(!previewMode);
  };

  const handleUndo = () => {
    // TODO: Implement undo functionality
    console.log('Undo');
  };

  const handleRedo = () => {
    // TODO: Implement redo functionality
    console.log('Redo');
  };

  const handleExport = () => {
    setShowExportDialog(true);
  };

  const handleSettings = () => {
    setShowSettingsDialog(true);
  };

  return (
    <AppBar 
      position="static" 
      color="default" 
      elevation={1}
      sx={{ 
        backgroundColor: '#f5f5f5',
        borderBottom: '1px solid #e0e0e0',
        zIndex: 1300,
      }}
    >
      <Toolbar sx={{ minHeight: '56px !important', px: 2 }}>
        {/* Project Title */}
        <Typography 
          variant="h6" 
          component="div" 
          sx={{ 
            flexGrow: 0, 
            mr: 3,
            color: '#333',
            fontWeight: 600,
          }}
        >
          {currentProject?.settings.title || 'E-Learning Authoring Tool'}
        </Typography>

        {/* File Operations */}
        <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
          <Button
            variant="outlined"
            size="small"
            onClick={handleNewProject}
            sx={{ minWidth: 'auto' }}
          >
            New
          </Button>
          
          <Tooltip title="Open Project">
            <IconButton size="small" onClick={handleOpen}>
              <OpenIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Save Project">
            <IconButton 
              size="small" 
              onClick={handleSave}
              disabled={!currentProject}
            >
              <SaveIcon />
            </IconButton>
          </Tooltip>
        </Box>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* Edit Operations */}
        <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
          <Tooltip title="Undo">
            <span>
              <IconButton 
                size="small" 
                onClick={handleUndo}
                disabled={!canUndo}
              >
                <UndoIcon />
              </IconButton>
            </span>
          </Tooltip>
          
          <Tooltip title="Redo">
            <span>
              <IconButton 
                size="small" 
                onClick={handleRedo}
                disabled={!canRedo}
              >
                <RedoIcon />
              </IconButton>
            </span>
          </Tooltip>
        </Box>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* Spacer */}
        <Box sx={{ flexGrow: 1 }} />

        {/* Preview and Export */}
        <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
          <Button
            variant={previewMode ? "contained" : "outlined"}
            size="small"
            startIcon={<PreviewIcon />}
            onClick={handlePreview}
            disabled={!currentProject}
            color={previewMode ? "primary" : "inherit"}
          >
            {previewMode ? 'Exit Preview' : 'Preview'}
          </Button>
          
          <Button
            variant="outlined"
            size="small"
            startIcon={<ExportIcon />}
            onClick={handleExport}
            disabled={!currentProject}
          >
            Export
          </Button>
        </Box>

        <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

        {/* Settings */}
        <Tooltip title="Settings">
          <IconButton size="small" onClick={handleSettings}>
            <SettingsIcon />
          </IconButton>
        </Tooltip>
      </Toolbar>
    </AppBar>
  );
};

export default Header;

import React from 'react';
import {
  <PERSON>,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { useProjectStore, useUIStore } from '../../store';

interface PropertiesPanelProps {
  width: number;
}

const PropertiesPanel: React.FC<PropertiesPanelProps> = ({ width }) => {
  const { currentProject, updateSlide } = useProjectStore();
  const { selectedSlideId, selectedElementIds, sidebarCollapsed } = useUIStore();

  // Get current slide and selected elements
  const currentSlide = currentProject?.slides.find(slide => slide.id === selectedSlideId);
  const selectedElements = currentSlide?.layers
    .flatMap(layer => layer.elements)
    .filter(element => selectedElementIds.includes(element.id)) || [];

  // Helper function to update an element
  const updateElement = (elementId: string, updates: any) => {
    if (!currentSlide) return;

    const updatedLayers = currentSlide.layers.map(layer => ({
      ...layer,
      elements: layer.elements.map(element =>
        element.id === elementId ? { ...element, ...updates } : element
      ),
    }));

    updateSlide(currentSlide.id, { layers: updatedLayers });
  };

  const renderSlideProperties = () => (
    <Accordion defaultExpanded>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle2">Slide Properties</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <TextField
            label="Slide Name"
            size="small"
            value={currentSlide?.name || ''}
            onChange={(e) => {
              if (currentSlide) {
                updateSlide(currentSlide.id, { name: e.target.value });
              }
            }}
          />
          
          <Box>
            <Typography variant="caption" gutterBottom>
              Background Color
            </Typography>
            <TextField
              type="color"
              size="small"
              value={currentSlide?.background.color || '#ffffff'}
              onChange={(e) => {
                if (currentSlide) {
                  updateSlide(currentSlide.id, {
                    background: { ...currentSlide.background, color: e.target.value }
                  });
                }
              }}
              sx={{ width: '100%' }}
            />
          </Box>

          <Box>
            <Typography variant="caption" gutterBottom>
              Dimensions
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                label="Width"
                size="small"
                type="number"
                value={currentSlide?.dimensions.width || 1920}
                onChange={(e) => {
                  // TODO: Update slide width
                  console.log('Update slide width:', e.target.value);
                }}
              />
              <TextField
                label="Height"
                size="small"
                type="number"
                value={currentSlide?.dimensions.height || 1080}
                onChange={(e) => {
                  // TODO: Update slide height
                  console.log('Update slide height:', e.target.value);
                }}
              />
            </Box>
          </Box>

          <TextField
            label="Notes"
            multiline
            rows={3}
            size="small"
            value={currentSlide?.notes || ''}
            onChange={(e) => {
              // TODO: Update slide notes
              console.log('Update slide notes:', e.target.value);
            }}
          />
        </Box>
      </AccordionDetails>
    </Accordion>
  );

  const renderElementProperties = () => {
    if (selectedElements.length === 0) {
      return (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Select an element to edit its properties
          </Typography>
        </Box>
      );
    }

    if (selectedElements.length > 1) {
      return (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Multiple elements selected
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Some properties may not be available
          </Typography>
        </Box>
      );
    }

    const element = selectedElements[0];

    return (
      <>
        {/* Transform Properties */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">Transform</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  label="X"
                  size="small"
                  type="number"
                  value={element.transform.position.x}
                  onChange={(e) => {
                    // TODO: Update element X position
                    console.log('Update X:', e.target.value);
                  }}
                />
                <TextField
                  label="Y"
                  size="small"
                  type="number"
                  value={element.transform.position.y}
                  onChange={(e) => {
                    // TODO: Update element Y position
                    console.log('Update Y:', e.target.value);
                  }}
                />
              </Box>
              
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  label="Width"
                  size="small"
                  type="number"
                  value={element.transform.size.width}
                  onChange={(e) => {
                    // TODO: Update element width
                    console.log('Update width:', e.target.value);
                  }}
                />
                <TextField
                  label="Height"
                  size="small"
                  type="number"
                  value={element.transform.size.height}
                  onChange={(e) => {
                    // TODO: Update element height
                    console.log('Update height:', e.target.value);
                  }}
                />
              </Box>

              <Box>
                <Typography variant="caption" gutterBottom>
                  Rotation: {element.transform.rotation}°
                </Typography>
                <Slider
                  value={element.transform.rotation}
                  min={-180}
                  max={180}
                  step={1}
                  onChange={(_, value) => {
                    // TODO: Update element rotation
                    console.log('Update rotation:', value);
                  }}
                />
              </Box>

              <Box>
                <Typography variant="caption" gutterBottom>
                  Opacity: {Math.round(element.opacity * 100)}%
                </Typography>
                <Slider
                  value={element.opacity}
                  min={0}
                  max={1}
                  step={0.01}
                  onChange={(_, value) => {
                    // TODO: Update element opacity
                    console.log('Update opacity:', value);
                  }}
                />
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Element-specific Properties */}
        {element.type === 'text' && (
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2">Text Properties</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  label="Text Content"
                  multiline
                  rows={3}
                  size="small"
                  value={(element as any).content || ''}
                  onChange={(e) => {
                    // TODO: Update text content
                    console.log('Update text content:', e.target.value);
                  }}
                />
                
                <TextField
                  label="Font Size"
                  size="small"
                  type="number"
                  value={(element as any).fontSize || 16}
                  onChange={(e) => {
                    // TODO: Update font size
                    console.log('Update font size:', e.target.value);
                  }}
                />

                <FormControl size="small">
                  <InputLabel>Font Family</InputLabel>
                  <Select
                    value={(element as any).fontFamily || 'Arial'}
                    label="Font Family"
                    onChange={(e) => {
                      // TODO: Update font family
                      console.log('Update font family:', e.target.value);
                    }}
                  >
                    <MenuItem value="Arial">Arial</MenuItem>
                    <MenuItem value="Helvetica">Helvetica</MenuItem>
                    <MenuItem value="Times New Roman">Times New Roman</MenuItem>
                    <MenuItem value="Georgia">Georgia</MenuItem>
                  </Select>
                </FormControl>

                <TextField
                  label="Text Color"
                  type="color"
                  size="small"
                  value={(element as any).color || '#000000'}
                  onChange={(e) => {
                    // TODO: Update text color
                    console.log('Update text color:', e.target.value);
                  }}
                />
              </Box>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Visibility and Locking */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">Visibility</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={element.visible}
                    onChange={(e) => {
                      // TODO: Update element visibility
                      console.log('Update visibility:', e.target.checked);
                    }}
                  />
                }
                label="Visible"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={element.locked}
                    onChange={(e) => {
                      // TODO: Update element lock state
                      console.log('Update locked:', e.target.checked);
                    }}
                  />
                }
                label="Locked"
              />
            </Box>
          </AccordionDetails>
        </Accordion>
      </>
    );
  };

  if (sidebarCollapsed.right) {
    return null;
  }

  return (
    <Box
      sx={{
        width,
        flex: 1,
        backgroundColor: '#fafafa',
        borderLeft: '1px solid #e0e0e0',
        borderBottom: '1px solid #e0e0e0',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: '1px solid #e0e0e0',
        }}
      >
        <Typography variant="subtitle2" fontWeight={600}>
          Properties
        </Typography>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {currentSlide && renderSlideProperties()}
        {selectedElements.length > 0 && <Divider />}
        {renderElementProperties()}
      </Box>
    </Box>
  );
};

export default PropertiesPanel;

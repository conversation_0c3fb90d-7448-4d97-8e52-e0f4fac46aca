import React, { useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  IconButton,
  ButtonGroup,
  Typography,
  Tooltip,
} from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  FitScreen as FitScreenIcon,
  CenterFocusStrong as ActualSizeIcon,
} from '@mui/icons-material';
import { useDrop } from 'react-dnd';
import { useProjectStore, useUIStore } from '../../store';
import { Element, TextElement, ImageElement, ButtonElement, ShapeElement } from '../../types';
import DraggableElement from '../slides/DraggableElement';

interface MainCanvasProps {
  width: number;
  height: number;
}

const MainCanvas: React.FC<MainCanvasProps> = ({ width, height }) => {
  const { currentProject, updateSlide } = useProjectStore();
  const {
    selectedSlideId,
    canvasZoom,
    canvasPosition,
    canvasFitMode,
    previewMode,
    setCanvasZoom,
    setCanvasPosition,
    zoomIn,
    zoomOut,
    zoomToFit,
    zoomToActual,
    setSelectedElements,
  } = useUIStore();

  const canvasRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Drop functionality
  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'ELEMENT',
    drop: (item: { elementType: string }, monitor) => {
      const offset = monitor.getClientOffset();
      const canvasRect = canvasRef.current?.getBoundingClientRect();

      if (offset && canvasRect && currentSlide) {
        const x = (offset.x - canvasRect.left) / canvasZoom;
        const y = (offset.y - canvasRect.top) / canvasZoom;

        createElement(item.elementType, x, y);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  const createElement = (elementType: string, x: number, y: number) => {
    if (!currentSlide) return;

    const baseElement = {
      id: `element-${Date.now()}`,
      name: `${elementType} ${Date.now()}`,
      transform: {
        position: { x, y },
        size: { width: 200, height: 100 },
        rotation: 0,
        scale: 1,
      },
      visible: true,
      locked: false,
      opacity: 1,
      zIndex: 1,
      animations: [],
      interactions: [],
    };

    let newElement: Element;

    switch (elementType) {
      case 'text':
        newElement = {
          ...baseElement,
          type: 'text',
          content: 'Sample Text',
          fontSize: 16,
          fontFamily: 'Arial',
          fontWeight: 'normal',
          fontStyle: 'normal',
          textAlign: 'left',
          color: '#000000',
          padding: { top: 8, right: 8, bottom: 8, left: 8 },
        } as TextElement;
        break;
      case 'image':
        newElement = {
          ...baseElement,
          type: 'image',
          src: '',
          alt: 'Image',
          fit: 'contain',
        } as ImageElement;
        break;
      case 'button':
        newElement = {
          ...baseElement,
          type: 'button',
          text: 'Button',
          backgroundColor: '#1976d2',
          textColor: '#ffffff',
          borderRadius: 4,
          fontSize: 14,
          fontFamily: 'Arial',
          fontWeight: 'normal',
          padding: { top: 8, right: 16, bottom: 8, left: 16 },
        } as ButtonElement;
        break;
      case 'shape':
        newElement = {
          ...baseElement,
          type: 'shape',
          shapeType: 'rectangle',
          fillColor: '#e0e0e0',
          strokeColor: '#000000',
          strokeWidth: 1,
        } as ShapeElement;
        break;
      default:
        return;
    }

    // Add element to the first layer of the current slide
    const updatedLayers = [...currentSlide.layers];
    updatedLayers[0] = {
      ...updatedLayers[0],
      elements: [...updatedLayers[0].elements, newElement],
    };

    updateSlide(currentSlide.id, { layers: updatedLayers });
    setSelectedElements([newElement.id]);
  };

  // Get current slide
  const currentSlide = currentProject?.slides.find(slide => slide.id === selectedSlideId) || currentProject?.slides[0];

  // Calculate canvas dimensions and positioning
  const slideAspectRatio = currentSlide ? currentSlide.dimensions.width / currentSlide.dimensions.height : 16/9;
  const containerAspectRatio = width / height;

  let canvasWidth: number;
  let canvasHeight: number;

  if (canvasFitMode === 'fit') {
    if (containerAspectRatio > slideAspectRatio) {
      canvasHeight = height * 0.8; // Leave some margin
      canvasWidth = canvasHeight * slideAspectRatio;
    } else {
      canvasWidth = width * 0.8;
      canvasHeight = canvasWidth / slideAspectRatio;
    }
  } else {
    canvasWidth = currentSlide ? currentSlide.dimensions.width * canvasZoom : width * 0.8;
    canvasHeight = currentSlide ? currentSlide.dimensions.height * canvasZoom : height * 0.8;
  }

  const handleZoomChange = (newZoom: number) => {
    setCanvasZoom(newZoom);
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    // Clear selection when clicking on empty canvas
    if (e.target === e.currentTarget) {
      setSelectedElements([]);
    }
  };

  const renderSlideContent = () => {
    if (!currentSlide) {
      return (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'text.secondary',
          }}
        >
          <Typography variant="h6">
            No slide selected
          </Typography>
        </Box>
      );
    }

    return (
      <Box
        onClick={handleCanvasClick}
        sx={{
          width: '100%',
          height: '100%',
          backgroundColor: currentSlide.background.color || '#ffffff',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Render slide layers and elements here */}
        {currentSlide.layers.map((layer) => (
          <Box
            key={layer.id}
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              opacity: layer.opacity,
              visibility: layer.visible ? 'visible' : 'hidden',
              pointerEvents: layer.locked ? 'none' : 'auto',
            }}
          >
            {/* Elements will be rendered here */}
            {layer.elements.map((element) => (
              <DraggableElement
                key={element.id}
                element={element}
                onUpdate={(elementId, updates) => {
                  // TODO: Implement element update
                  console.log('Update element:', elementId, updates);
                }}
              />
            ))}
          </Box>
        ))}
        
        {/* Placeholder content when slide is empty */}
        {currentSlide.layers.every(layer => layer.elements.length === 0) && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: 'text.secondary',
            }}
          >
            <Typography variant="h6" gutterBottom>
              {currentSlide.name}
            </Typography>
            <Typography variant="body2">
              Drag elements here to start building your slide
            </Typography>
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box
      sx={{
        width,
        height,
        backgroundColor: '#f0f0f0',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Zoom Controls */}
      {!previewMode && (
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            zIndex: 10,
          }}
        >
          <Paper elevation={2}>
            <ButtonGroup orientation="vertical" size="small">
              <Tooltip title="Zoom In">
                <IconButton onClick={zoomIn}>
                  <ZoomInIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Zoom Out">
                <IconButton onClick={zoomOut}>
                  <ZoomOutIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Fit to Window">
                <IconButton onClick={zoomToFit}>
                  <FitScreenIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Actual Size">
                <IconButton onClick={zoomToActual}>
                  <ActualSizeIcon />
                </IconButton>
              </Tooltip>
            </ButtonGroup>
          </Paper>
        </Box>
      )}

      {/* Zoom Level Display */}
      {!previewMode && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            right: 16,
            zIndex: 10,
          }}
        >
          <Paper elevation={1} sx={{ px: 1, py: 0.5 }}>
            <Typography variant="caption">
              {Math.round(canvasZoom * 100)}%
            </Typography>
          </Paper>
        </Box>
      )}

      {/* Canvas Container */}
      <Box
        ref={containerRef}
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'auto',
        }}
      >
        {/* Canvas */}
        <Paper
          ref={(node) => {
            canvasRef.current = node;
            drop(node);
          }}
          elevation={4}
          sx={{
            width: canvasWidth,
            height: canvasHeight,
            backgroundColor: isOver ? '#e3f2fd' : '#ffffff',
            position: 'relative',
            cursor: previewMode ? 'default' : 'crosshair',
            border: isOver ? '2px dashed #1976d2' : 'none',
          }}
        >
          {renderSlideContent()}
        </Paper>
      </Box>

      {/* Rulers (placeholder for future implementation) */}
      {!previewMode && (
        <>
          {/* Horizontal Ruler */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: 20,
              backgroundColor: '#e0e0e0',
              borderBottom: '1px solid #ccc',
              display: 'flex',
              alignItems: 'center',
              fontSize: '10px',
              color: 'text.secondary',
              paddingLeft: 1,
            }}
          >
            Horizontal Ruler
          </Box>
          
          {/* Vertical Ruler */}
          <Box
            sx={{
              position: 'absolute',
              top: 20,
              left: 0,
              bottom: 0,
              width: 20,
              backgroundColor: '#e0e0e0',
              borderRight: '1px solid #ccc',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              color: 'text.secondary',
              writingMode: 'vertical-rl',
            }}
          >
            Vertical Ruler
          </Box>
        </>
      )}
    </Box>
  );
};

export default MainCanvas;

import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TextFields as TextIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
  AudioFile as AudioIcon,
  SmartButton as ButtonIcon,
  TouchApp as HotspotIcon,
  Quiz as QuizIcon,
  Crop75 as ShapeIcon,
} from '@mui/icons-material';
import { useDrag } from 'react-dnd';

interface DraggableElementProps {
  type: string;
  icon: React.ReactNode;
  label: string;
}

const DraggableElement: React.FC<DraggableElementProps> = ({ type, icon, label }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'ELEMENT',
    item: { elementType: type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <ListItem
      ref={drag}
      sx={{
        cursor: 'grab',
        opacity: isDragging ? 0.5 : 1,
        '&:hover': {
          backgroundColor: 'action.hover',
        },
        borderRadius: 1,
        mb: 0.5,
      }}
    >
      <ListItemIcon sx={{ minWidth: 36 }}>
        {icon}
      </ListItemIcon>
      <ListItemText 
        primary={label} 
        primaryTypographyProps={{ variant: 'body2' }}
      />
    </ListItem>
  );
};

interface AssetLibraryProps {
  width: number;
}

const AssetLibrary: React.FC<AssetLibraryProps> = ({ width }) => {
  const elementTypes = [
    { type: 'text', icon: <TextIcon />, label: 'Text' },
    { type: 'image', icon: <ImageIcon />, label: 'Image' },
    { type: 'video', icon: <VideoIcon />, label: 'Video' },
    { type: 'audio', icon: <AudioIcon />, label: 'Audio' },
    { type: 'button', icon: <ButtonIcon />, label: 'Button' },
    { type: 'hotspot', icon: <HotspotIcon />, label: 'Hotspot' },
    { type: 'quiz', icon: <QuizIcon />, label: 'Quiz' },
    { type: 'shape', icon: <ShapeIcon />, label: 'Shape' },
  ];

  return (
    <Box
      sx={{
        width,
        height: '100%',
        backgroundColor: '#fafafa',
        borderTop: '1px solid #e0e0e0',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: '1px solid #e0e0e0',
        }}
      >
        <Typography variant="subtitle2" fontWeight={600}>
          Asset Library
        </Typography>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {/* Elements Section */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">Elements</Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 1 }}>
            <List dense>
              {elementTypes.map((element) => (
                <DraggableElement
                  key={element.type}
                  type={element.type}
                  icon={element.icon}
                  label={element.label}
                />
              ))}
            </List>
          </AccordionDetails>
        </Accordion>

        {/* Media Assets Section */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">Media Assets</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="body2" color="text.secondary">
                No media assets uploaded
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Drag and drop files here to upload
              </Typography>
            </Box>
          </AccordionDetails>
        </Accordion>

        {/* Templates Section */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">Templates</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Typography variant="body2" color="text.secondary">
                No templates available
              </Typography>
            </Box>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Box>
  );
};

export default AssetLibrary;

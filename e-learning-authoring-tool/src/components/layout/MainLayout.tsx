import React, { useEffect } from 'react';
import { Box, CssBaseline, ThemeProvider, createTheme } from '@mui/material';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import Header from './Header';
import SlideNavigator from './SlideNavigator';
import MainCanvas from './MainCanvas';
import PropertiesPanel from './PropertiesPanel';
import AssetLibrary from './AssetLibrary';
import { useProjectStore, useUIStore } from '../../store';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#ffffff',
          color: '#333333',
        },
      },
    },
  },
});

const HEADER_HEIGHT = 56;
const SLIDE_NAVIGATOR_WIDTH = 250;
const PROPERTIES_PANEL_WIDTH = 300;
const TIMELINE_HEIGHT = 200;

const MainLayout: React.FC = () => {
  const { currentProject, createNewProject } = useProjectStore();
  const { 
    sidebarCollapsed, 
    timelineCollapsed, 
    selectedSlideId, 
    setSelectedSlide 
  } = useUIStore();

  // Create a default project if none exists
  useEffect(() => {
    if (!currentProject) {
      createNewProject({
        title: 'My First Project',
        author: 'User',
      });
    }
  }, [currentProject, createNewProject]);

  // Set the first slide as selected if none is selected
  useEffect(() => {
    if (currentProject && !selectedSlideId && currentProject.slides.length > 0) {
      setSelectedSlide(currentProject.slides[0].id);
    }
  }, [currentProject, selectedSlideId, setSelectedSlide]);

  // Calculate layout dimensions
  const leftSidebarWidth = sidebarCollapsed.left ? 0 : SLIDE_NAVIGATOR_WIDTH;
  const rightSidebarWidth = sidebarCollapsed.right ? 0 : PROPERTIES_PANEL_WIDTH;
  const timelineHeight = timelineCollapsed ? 0 : TIMELINE_HEIGHT;
  
  const canvasWidth = window.innerWidth - leftSidebarWidth - rightSidebarWidth;
  const canvasHeight = window.innerHeight - HEADER_HEIGHT - timelineHeight;

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <DndProvider backend={HTML5Backend}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'hidden',
          }}
        >
          {/* Header */}
          <Header />

          {/* Main Content Area */}
          <Box
            sx={{
              display: 'flex',
              flex: 1,
              overflow: 'hidden',
            }}
          >
            {/* Left Sidebar - Slide Navigator */}
            <SlideNavigator width={leftSidebarWidth} />

            {/* Center - Main Canvas */}
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
              }}
            >
              {/* Canvas Area */}
              <Box sx={{ flex: 1, overflow: 'hidden' }}>
                <MainCanvas width={canvasWidth} height={canvasHeight} />
              </Box>

              {/* Timeline Panel (Bottom) */}
              {!timelineCollapsed && (
                <Box
                  sx={{
                    height: timelineHeight,
                    backgroundColor: '#f0f0f0',
                    borderTop: '1px solid #e0e0e0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
                    Timeline Panel (Coming Soon)
                  </Box>
                </Box>
              )}
            </Box>

            {/* Right Sidebar - Properties Panel and Asset Library */}
            <Box
              sx={{
                width: rightSidebarWidth,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <PropertiesPanel width={rightSidebarWidth} />
              <AssetLibrary width={rightSidebarWidth} />
            </Box>
          </Box>
        </Box>
      </DndProvider>
    </ThemeProvider>
  );
};

export default MainLayout;
